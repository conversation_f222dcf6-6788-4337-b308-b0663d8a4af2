// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Bundle {
    #[prost(message, optional, tag = "2")]
    pub header: ::core::option::Option<super::shared::Header>,
    #[prost(message, repeated, tag = "3")]
    pub packets: ::prost::alloc::vec::Vec<super::packet::Packet>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct BundleUuid {
    #[prost(message, optional, tag = "1")]
    pub bundle: ::core::option::Option<Bundle>,
    #[prost(string, tag = "2")]
    pub uuid: ::prost::alloc::string::String,
}
/// Indicates the bundle was accepted and forwarded to a validator.
/// NOTE: A single bundle may have multiple events emitted if forwarded to many validators.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Accepted {
    /// Slot at which bundle was forwarded.
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    /// Validator identity bundle was forwarded to.
    #[prost(string, tag = "2")]
    pub validator_identity: ::prost::alloc::string::String,
}
/// Indicates the bundle was dropped and therefore not forwarded to any validator.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Rejected {
    #[prost(oneof = "rejected::Reason", tags = "1, 2, 3, 4, 5")]
    pub reason: ::core::option::Option<rejected::Reason>,
}
/// Nested message and enum types in `Rejected`.
pub mod rejected {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Reason {
        #[prost(message, tag = "1")]
        StateAuctionBidRejected(super::StateAuctionBidRejected),
        #[prost(message, tag = "2")]
        WinningBatchBidRejected(super::WinningBatchBidRejected),
        #[prost(message, tag = "3")]
        SimulationFailure(super::SimulationFailure),
        #[prost(message, tag = "4")]
        InternalError(super::InternalError),
        #[prost(message, tag = "5")]
        DroppedBundle(super::DroppedBundle),
    }
}
/// Indicates the bundle's bid was high enough to win its state auction.
/// However, not high enough relative to other state auction winners and therefore excluded from being forwarded.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WinningBatchBidRejected {
    /// Auction's unique identifier.
    #[prost(string, tag = "1")]
    pub auction_id: ::prost::alloc::string::String,
    /// Bundle's simulated bid.
    #[prost(uint64, tag = "2")]
    pub simulated_bid_lamports: u64,
    #[prost(string, optional, tag = "3")]
    pub msg: ::core::option::Option<::prost::alloc::string::String>,
}
/// Indicates the bundle's bid was __not__ high enough to be included in its state auction's set of winners.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StateAuctionBidRejected {
    /// Auction's unique identifier.
    #[prost(string, tag = "1")]
    pub auction_id: ::prost::alloc::string::String,
    /// Bundle's simulated bid.
    #[prost(uint64, tag = "2")]
    pub simulated_bid_lamports: u64,
    #[prost(string, optional, tag = "3")]
    pub msg: ::core::option::Option<::prost::alloc::string::String>,
}
/// Bundle dropped due to simulation failure.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SimulationFailure {
    /// Signature of the offending transaction.
    #[prost(string, tag = "1")]
    pub tx_signature: ::prost::alloc::string::String,
    #[prost(string, optional, tag = "2")]
    pub msg: ::core::option::Option<::prost::alloc::string::String>,
}
/// Bundle dropped due to an internal error.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct InternalError {
    #[prost(string, tag = "1")]
    pub msg: ::prost::alloc::string::String,
}
/// Bundle dropped (e.g. because no leader upcoming)
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DroppedBundle {
    #[prost(string, tag = "1")]
    pub msg: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct Finalized {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Processed {
    #[prost(string, tag = "1")]
    pub validator_identity: ::prost::alloc::string::String,
    #[prost(uint64, tag = "2")]
    pub slot: u64,
    /// / Index within the block.
    #[prost(uint64, tag = "3")]
    pub bundle_index: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct Dropped {
    #[prost(enumeration = "DroppedReason", tag = "1")]
    pub reason: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct BundleResult {
    /// Bundle's Uuid.
    #[prost(string, tag = "1")]
    pub bundle_id: ::prost::alloc::string::String,
    #[prost(oneof = "bundle_result::Result", tags = "2, 3, 4, 5, 6")]
    pub result: ::core::option::Option<bundle_result::Result>,
}
/// Nested message and enum types in `BundleResult`.
pub mod bundle_result {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Result {
        /// Indicated accepted by the block-engine and forwarded to a jito-solana validator.
        #[prost(message, tag = "2")]
        Accepted(super::Accepted),
        /// Rejected by the block-engine.
        #[prost(message, tag = "3")]
        Rejected(super::Rejected),
        /// Reached finalized commitment level.
        #[prost(message, tag = "4")]
        Finalized(super::Finalized),
        /// Reached a processed commitment level.
        #[prost(message, tag = "5")]
        Processed(super::Processed),
        /// Was accepted and forwarded by the block-engine but never landed on-chain.
        #[prost(message, tag = "6")]
        Dropped(super::Dropped),
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum DroppedReason {
    BlockhashExpired = 0,
    /// One or more transactions in the bundle landed on-chain, invalidating the bundle.
    PartiallyProcessed = 1,
    /// This indicates bundle was processed but not finalized. This could occur during forks.
    NotFinalized = 2,
}
impl DroppedReason {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::BlockhashExpired => "BlockhashExpired",
            Self::PartiallyProcessed => "PartiallyProcessed",
            Self::NotFinalized => "NotFinalized",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "BlockhashExpired" => Some(Self::BlockhashExpired),
            "PartiallyProcessed" => Some(Self::PartiallyProcessed),
            "NotFinalized" => Some(Self::NotFinalized),
            _ => None,
        }
    }
}
