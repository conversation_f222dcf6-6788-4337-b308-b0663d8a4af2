// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PostSubmitRequest {
    #[prost(message, optional, tag = "1")]
    pub transaction: ::core::option::Option<TransactionMessage>,
    #[prost(bool, tag = "2")]
    pub skip_pre_flight: bool,
    #[prost(bool, optional, tag = "3")]
    pub front_running_protection: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "8")]
    pub experimental_front_running_protection: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "9")]
    pub snipe_transaction: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PostSubmitRequestEntry {
    #[prost(message, optional, tag = "1")]
    pub transaction: ::core::option::Option<TransactionMessage>,
    #[prost(bool, tag = "2")]
    pub skip_pre_flight: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PostSubmitBatchRequest {
    #[prost(message, repeated, tag = "1")]
    pub entries: ::prost::alloc::vec::Vec<PostSubmitRequestEntry>,
    #[prost(enumeration = "SubmitStrategy", tag = "2")]
    pub submit_strategy: i32,
    #[prost(bool, optional, tag = "3")]
    pub use_bundle: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "4")]
    pub front_running_protection: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PostSubmitBatchResponseEntry {
    #[prost(string, tag = "1")]
    pub signature: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub error: ::prost::alloc::string::String,
    #[prost(bool, tag = "3")]
    pub submitted: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PostSubmitBatchResponse {
    #[prost(message, repeated, tag = "1")]
    pub transactions: ::prost::alloc::vec::Vec<PostSubmitBatchResponseEntry>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PostSubmitResponse {
    #[prost(string, tag = "1")]
    pub signature: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TransactionMessage {
    #[prost(string, tag = "1")]
    pub content: ::prost::alloc::string::String,
    #[prost(bool, tag = "2")]
    pub is_cleanup: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TransactionMessageV2 {
    #[prost(string, tag = "1")]
    pub content: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SubmitStrategy {
    PUknown = 0,
    PSubmitAll = 1,
    PAbortOnFirstError = 2,
    PWaitForConfirmation = 3,
}
impl SubmitStrategy {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::PUknown => "P_UKNOWN",
            Self::PSubmitAll => "P_SUBMIT_ALL",
            Self::PAbortOnFirstError => "P_ABORT_ON_FIRST_ERROR",
            Self::PWaitForConfirmation => "P_WAIT_FOR_CONFIRMATION",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "P_UKNOWN" => Some(Self::PUknown),
            "P_SUBMIT_ALL" => Some(Self::PSubmitAll),
            "P_ABORT_ON_FIRST_ERROR" => Some(Self::PAbortOnFirstError),
            "P_WAIT_FOR_CONFIRMATION" => Some(Self::PWaitForConfirmation),
            _ => None,
        }
    }
}
/// Generated client implementations.
pub mod api_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct ApiClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl ApiClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: std::convert::TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> ApiClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> ApiClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            ApiClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn post_submit_v2(
            &mut self,
            request: impl tonic::IntoRequest<super::PostSubmitRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PostSubmitResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/api.Api/PostSubmitV2");
            let mut req = request.into_request();
            req.extensions_mut().insert(GrpcMethod::new("api.Api", "PostSubmitV2"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_submit_batch_v2(
            &mut self,
            request: impl tonic::IntoRequest<super::PostSubmitBatchRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PostSubmitBatchResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/api.Api/PostSubmitBatchV2",
            );
            let mut req = request.into_request();
            req.extensions_mut().insert(GrpcMethod::new("api.Api", "PostSubmitBatchV2"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod api_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with ApiServer.
    #[async_trait]
    pub trait Api: std::marker::Send + std::marker::Sync + 'static {
        async fn post_submit_v2(
            &self,
            request: tonic::Request<super::PostSubmitRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PostSubmitResponse>,
            tonic::Status,
        >;
        async fn post_submit_batch_v2(
            &self,
            request: tonic::Request<super::PostSubmitBatchRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PostSubmitBatchResponse>,
            tonic::Status,
        >;
    }
    #[derive(Debug)]
    pub struct ApiServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> ApiServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for ApiServer<T>
    where
        T: Api,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::BoxBody>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/api.Api/PostSubmitV2" => {
                    #[allow(non_camel_case_types)]
                    struct PostSubmitV2Svc<T: Api>(pub Arc<T>);
                    impl<T: Api> tonic::server::UnaryService<super::PostSubmitRequest>
                    for PostSubmitV2Svc<T> {
                        type Response = super::PostSubmitResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PostSubmitRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Api>::post_submit_v2(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostSubmitV2Svc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/api.Api/PostSubmitBatchV2" => {
                    #[allow(non_camel_case_types)]
                    struct PostSubmitBatchV2Svc<T: Api>(pub Arc<T>);
                    impl<
                        T: Api,
                    > tonic::server::UnaryService<super::PostSubmitBatchRequest>
                    for PostSubmitBatchV2Svc<T> {
                        type Response = super::PostSubmitBatchResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PostSubmitBatchRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Api>::post_submit_batch_v2(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostSubmitBatchV2Svc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(empty_body());
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for ApiServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "api.Api";
    impl<T> tonic::server::NamedService for ApiServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}