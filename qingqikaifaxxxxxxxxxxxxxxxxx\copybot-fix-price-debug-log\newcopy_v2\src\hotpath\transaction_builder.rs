use crate::{
    shared::{
        types::{HotPathTrade, WalletConfig},
    },
    blockhash_service::BlockhashService,
    config::FeeConfig,
};
use solana_sdk::{
    hash::Hash,
    instruction::{AccountMeta, Instruction},
    message::Message,
    pubkey::Pubkey,
    signature::{Keypair, Signer, Signature},
    system_instruction,
    system_program,
    compute_budget::ComputeBudgetInstruction,
    transaction::Transaction,
};
use std::sync::Arc;

use tracing::{debug, warn, Level, info};
use hex;
use anyhow::{anyhow, Result};
use tokio;

// --- Constants ---
const PUMP_FUN_PROGRAM_ID: Pubkey = solana_sdk::pubkey!("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P");
const GLOBAL_ACCOUNT: Pubkey = solana_sdk::pubkey!("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf");
const FEE_RECIPIENT: Pubkey = solana_sdk::pubkey!("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV");
const EVENT_AUTHORITY: Pubkey = solana_sdk::pubkey!("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1");
const TOKEN_PROGRAM_ID: Pubkey = spl_token::ID;
const SYSTEM_PROGRAM_ID: Pubkey = system_program::ID;
const BUY_DISCRIMINATOR: [u8; 8] = [102, 6, 61, 18, 1, 218, 235, 234];
const SELL_DISCRIMINATOR: [u8; 8] = [51, 230, 133, 164, 1, 127, 131, 173];

/// Helper to create a transfer instruction's data payload.
fn create_transfer_data(lamports: u64) -> Vec<u8> {
    let instruction_discriminator: [u8; 4] = [2, 0, 0, 0]; // Discriminator for SystemProgram::Transfer
    let mut data = Vec::with_capacity(12);
    data.extend_from_slice(&instruction_discriminator);
    data.extend_from_slice(&lamports.to_le_bytes());
    data
}

/// 根据加速器类型获取最低tip限制
fn get_min_tip_lamports_for_accelerator(accelerator_provider: &str) -> u64 {
    match accelerator_provider {
        "astralane" => 10_000,       // Astralane: 0.00001 SOL (官方要求)
        "blockrazor" => 1_000_000,   // BlockRazor: 0.001 SOL (官方要求)
        "oslot" => 100_000,          // Oslot: 0.0001 SOL
        "flashblock" => 50_000,      // Flashblock: 0.00005 SOL (更低的最低要求)
        _ => 100_000,                // 默认: 0.0001 SOL
    }
}

/// Builds transactions ultra-efficiently by caching pre-sorted `Message` templates.
pub struct TransactionBuilder {
    wallet: Arc<Keypair>,
    blockhash_service: Arc<BlockhashService>,
    blockhash_lag_slots: usize,
    fee_config: FeeConfig,

    // --- Buy Template ---
    buy_template: Message,
    buy_main_ix_idx: usize,
    buy_tip_ix_idx: usize,
    buy_fee_ix_idx: usize,
    buy_compute_limit_ix_idx: usize,
    buy_compute_price_ix_idx: usize,
    // Key indices in the sorted account_keys list
    buy_mint_key_idx: u8,
    buy_bonding_curve_key_idx: u8,
    buy_assoc_bc_key_idx: u8,
    buy_user_ata_key_idx: u8,
    buy_creator_vault_key_idx: u8,
    buy_tip_receiver_key_idx: u8,
    buy_fee_receiver_key_idx: u8,

    // --- Sell Template ---
    sell_template: Message,
    sell_main_ix_idx: usize,
    sell_tip_ix_idx: usize,
    sell_fee_ix_idx: usize,
    sell_compute_limit_ix_idx: usize,
    sell_compute_price_ix_idx: usize,
    // Key indices
    sell_mint_key_idx: u8,
    sell_bonding_curve_key_idx: u8,
    sell_assoc_bc_key_idx: u8,
    sell_user_ata_key_idx: u8,
    sell_creator_vault_key_idx: u8,
    sell_tip_receiver_key_idx: u8,
    sell_fee_receiver_key_idx: u8,

     // --- Sell & Close Template ---
    sell_close_template: Message,
    sell_close_main_ix_idx: usize,
    sell_close_tip_ix_idx: usize,
    sell_close_fee_ix_idx: usize,
    sell_close_compute_limit_ix_idx: usize,
    sell_close_compute_price_ix_idx: usize,
    // Key indices
    sell_close_mint_key_idx: u8,
    sell_close_bonding_curve_key_idx: u8,
    sell_close_assoc_bc_key_idx: u8,
    sell_close_user_ata_key_idx: u8,
    sell_close_creator_vault_key_idx: u8,
    sell_close_tip_receiver_key_idx: u8,
    sell_close_fee_receiver_key_idx: u8,
}

impl TransactionBuilder {
    pub fn new(
        wallet: Arc<Keypair>, 
        blockhash_service: Arc<BlockhashService>, 
        blockhash_lag_slots: usize,
        fee_config: FeeConfig,
    ) -> Self {
        let wallet_pubkey = wallet.pubkey();

        // --- BUY TEMPLATE ---
        let (
            buy_template,
            buy_main_ix_idx,
            buy_tip_ix_idx,
            buy_fee_ix_idx,
            buy_compute_limit_ix_idx,
            buy_compute_price_ix_idx,
            buy_mint_key_idx,
            buy_bonding_curve_key_idx,
            buy_assoc_bc_key_idx,
            buy_user_ata_key_idx,
            buy_creator_vault_key_idx,
            buy_tip_receiver_key_idx,
            buy_fee_receiver_key_idx
        ) = Self::create_buy_template(&wallet_pubkey, &fee_config);

        // --- SELL TEMPLATE ---
        let (
            sell_template,
            sell_main_ix_idx,
            sell_tip_ix_idx,
            sell_fee_ix_idx,
            sell_compute_limit_ix_idx,
            sell_compute_price_ix_idx,
            sell_mint_key_idx,
            sell_bonding_curve_key_idx,
            sell_assoc_bc_key_idx,
            sell_user_ata_key_idx,
            sell_creator_vault_key_idx,
            sell_tip_receiver_key_idx,
            sell_fee_receiver_key_idx
        ) = Self::create_sell_template(&wallet_pubkey, &fee_config, false);

        // --- SELL & CLOSE TEMPLATE ---
        let (
            sell_close_template,
            sell_close_main_ix_idx,
            sell_close_tip_ix_idx,
            sell_close_fee_ix_idx,
            sell_close_compute_limit_ix_idx,
            sell_close_compute_price_ix_idx,
            sell_close_mint_key_idx,
            sell_close_bonding_curve_key_idx,
            sell_close_assoc_bc_key_idx,
            sell_close_user_ata_key_idx,
            sell_close_creator_vault_key_idx,
            sell_close_tip_receiver_key_idx,
            sell_close_fee_receiver_key_idx
        ) = Self::create_sell_template(&wallet_pubkey, &fee_config, true);

        Self {
            wallet,
            blockhash_service,
            blockhash_lag_slots,
            fee_config,
            buy_template,
            buy_main_ix_idx,
            buy_tip_ix_idx,
            buy_fee_ix_idx,
            buy_compute_limit_ix_idx,
            buy_compute_price_ix_idx,
            buy_mint_key_idx,
            buy_bonding_curve_key_idx,
            buy_assoc_bc_key_idx,
            buy_user_ata_key_idx,
            buy_creator_vault_key_idx,
            buy_tip_receiver_key_idx,
            buy_fee_receiver_key_idx,
            sell_template,
            sell_main_ix_idx,
            sell_tip_ix_idx,
            sell_fee_ix_idx,
            sell_compute_limit_ix_idx,
            sell_compute_price_ix_idx,
            sell_mint_key_idx,
            sell_bonding_curve_key_idx,
            sell_assoc_bc_key_idx,
            sell_user_ata_key_idx,
            sell_creator_vault_key_idx,
            sell_tip_receiver_key_idx,
            sell_fee_receiver_key_idx,
            sell_close_template,
            sell_close_main_ix_idx,
            sell_close_tip_ix_idx,
            sell_close_fee_ix_idx,
            sell_close_compute_limit_ix_idx,
            sell_close_compute_price_ix_idx,
            sell_close_mint_key_idx,
            sell_close_bonding_curve_key_idx,
            sell_close_assoc_bc_key_idx,
            sell_close_user_ata_key_idx,
            sell_close_creator_vault_key_idx,
            sell_close_tip_receiver_key_idx,
            sell_close_fee_receiver_key_idx,
        }
    }

    /// Core hot-path function: builds an unsigned transaction from the message template.
    /// This is the single, unified function for buying, which always includes
    /// an idempotent ATA creation instruction.
    #[inline(always)]
    pub fn build_buy_transaction(
        &self,
        trade_info: &HotPathTrade,
        target_token_amount: u64,
        max_sol_cost_lamports: u64,
        wallet_config: &WalletConfig,
        tip_account: Pubkey,
        accelerator_provider: Option<&str>,
    ) -> Result<Transaction, anyhow::Error> {
        info!("🔍 进入build_buy_transaction函数");
        let mut message = self.buy_template.clone();

        // 1. Overwrite dynamic accounts
        let keys = &mut message.account_keys;
        keys[self.buy_mint_key_idx as usize] = trade_info.mint_pubkey;
        keys[self.buy_bonding_curve_key_idx as usize] = trade_info.bonding_curve_pubkey;
        keys[self.buy_assoc_bc_key_idx as usize] = trade_info.associated_bonding_curve;
        keys[self.buy_user_ata_key_idx as usize] = trade_info.user_ata;
        keys[self.buy_creator_vault_key_idx as usize] = trade_info.creator_vault_pubkey;
        keys[self.buy_tip_receiver_key_idx as usize] = tip_account;
        
        // 设置手续费接收者地址（如果启用）
        if self.fee_config.enabled {
            if let Some(fee_receiver_pubkey) = self.fee_config.fee_pubkey {
                keys[self.buy_fee_receiver_key_idx as usize] = fee_receiver_pubkey;
            }
        }

        // The user_ata_key is also used by the create_ata instruction,
        // but since it's the same key, we only need to set it once in the account list.

        // 2. Overwrite instruction data
        let main_ix = &mut message.instructions[self.buy_main_ix_idx];
        main_ix.data = self.build_instruction_data(target_token_amount, max_sol_cost_lamports, true);
        
        let tip_ix = &mut message.instructions[self.buy_tip_ix_idx];
        let tip_lamports = ((max_sol_cost_lamports as f64) * (wallet_config.accelerator_tip_percentage.unwrap_or(1.0) / 100.0)) as u64;
        // 根据加速器类型获取最低tip限制
        let min_tip = get_min_tip_lamports_for_accelerator(accelerator_provider.unwrap_or(""));
        let final_tip_lamports = tip_lamports.max(min_tip);
        tip_ix.data = create_transfer_data(final_tip_lamports);

        // 2.2. 处理手续费指令（零延迟，从内存配置读取）
        if self.fee_config.enabled {
            let fee_ix = &mut message.instructions[self.buy_fee_ix_idx];
            let fee_lamports = ((max_sol_cost_lamports as f64) * self.fee_config.fee_percentage) as u64;
            fee_ix.data = create_transfer_data(fee_lamports);
            debug!("买入手续费: {} lamports ({}%)", fee_lamports, self.fee_config.fee_percentage * 100.0);
        }
        
        // 2.1. Update compute budget instructions with wallet config values
        let compute_limit_ix = &mut message.instructions[self.buy_compute_limit_ix_idx];
        let compute_limit = if wallet_config.compute_unit_limit > 0 { 
            wallet_config.compute_unit_limit 
        } else { 
            80_000 
        };
        let new_limit_instruction = ComputeBudgetInstruction::set_compute_unit_limit(compute_limit);
        compute_limit_ix.data = new_limit_instruction.data;
        
        let compute_price_ix = &mut message.instructions[self.buy_compute_price_ix_idx];
        let compute_price = if wallet_config.priority_fee > 0 { 
            wallet_config.priority_fee 
        } else { 
            95_000 
        };
        let new_price_instruction = ComputeBudgetInstruction::set_compute_unit_price(compute_price);
        compute_price_ix.data = new_price_instruction.data;
        
        // 3. 🚀 使用nonce构建交易
        use crate::services::nonce_helper::{get_transaction_blockhash, is_using_nonce};
        use crate::services::nonce_pool::NonceAccountPool;
        
        let using_nonce = is_using_nonce();
        info!("🔍 检查nonce状态: using_nonce={}", using_nonce);
        
        if using_nonce {
            info!("🚀 开始构建nonce交易");
            
            // 获取nonce账户信息
            let pool = NonceAccountPool::get_instance();
            if let Some(nonce_account_info) = pool.get_next_available_account() {
                info!("🎯 使用nonce账户: {} (nonce: {})", nonce_account_info.address, nonce_account_info.current_nonce);
                
                // 设置nonce值作为blockhash
                message.recent_blockhash = nonce_account_info.current_nonce;
                info!("🎯 设置交易blockhash为nonce值: {}", nonce_account_info.current_nonce);
                
                // 创建advance nonce指令 - 使用nonce账户的权限地址作为授权者
                // 注意：nonce账户创建时authority设为payer.pubkey()，所以这里也必须使用相同的地址
                let advance_nonce_ix = system_instruction::advance_nonce_account(
                    &nonce_account_info.address,
                    &self.wallet.pubkey(),  // 确保与nonce账户创建时的authority一致
                );
                
                // 重新构建包含nonce指令的交易
                let original_transaction = Transaction::new_unsigned(message);
                
                // 重建所有指令，nonce指令在前
                let mut all_instructions = vec![advance_nonce_ix];
                
                // 添加原有指令
                for compiled_ix in &original_transaction.message.instructions {
                    let instruction = Instruction {
                        program_id: original_transaction.message.account_keys[compiled_ix.program_id_index as usize],
                        accounts: compiled_ix.accounts.iter().map(|&account_index| {
                            AccountMeta {
                                pubkey: original_transaction.message.account_keys[account_index as usize],
                                is_signer: original_transaction.message.is_signer(account_index as usize),
                                is_writable: original_transaction.message.is_writable(account_index as usize),
                            }
                        }).collect(),
                        data: compiled_ix.data.clone(),
                    };
                    all_instructions.push(instruction);
                }
                
                // 重新创建消息，包含nonce指令
                let new_message = Message::new_with_blockhash(
                    &all_instructions,
                    Some(&self.wallet.pubkey()),
                    &nonce_account_info.current_nonce,  // 使用nonce值作为blockhash
                );
                
                info!("🚀 成功构建nonce交易，包含{}个指令", all_instructions.len());
                
                // 🚀 重要：立即标记当前nonce账户为已使用，避免重复使用相同nonce值
                // 这必须在事务成功构建后立即执行，确保nonce值不会被重复使用
                pool.release_account(&nonce_account_info.address);
                info!("🔒 标记nonce账户为已使用: {}", nonce_account_info.address);
                
                return Ok(Transaction::new_unsigned(new_message));
            } else {
                info!("⚠️ 无可用nonce账户，使用普通交易");
            }
        }
        
        // 回退到普通区块哈希交易
        message.recent_blockhash = self
            .blockhash_service
            .get_blockhash_with_lag(self.blockhash_lag_slots)
            .map(|d| d.hash)
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;
        
        info!("🔄 使用普通区块哈希交易: {}", message.recent_blockhash);

        Ok(Transaction::new_unsigned(message))
    }
    
    pub fn get_wallet_pubkey(&self) -> Pubkey {
        self.wallet.pubkey()
    }

    pub async fn sign_and_log_details(&self, mut transaction: Transaction) -> Result<(Transaction, Signature, Hash)> {
        let wallet = self.wallet.clone();
        let recent_blockhash = transaction.message.recent_blockhash;
        
        // 使用 spawn_blocking 将同步的、CPU密集的签名操作移到专门的线程池
        // 从而避免阻塞 Tokio 的主异步运行时
        transaction = tokio::task::spawn_blocking(move || {
            transaction
                .try_partial_sign(&[wallet.as_ref()], recent_blockhash)
                .map_err(|e| anyhow!("交易签名失败: {}", e))?;
            Ok::<_, anyhow::Error>(transaction)
        })
        .await
        .map_err(|e| anyhow!("签名任务 paniced 或被取消: {}", e))??;
        
        let signature = transaction.signatures[0];
        
        self.log_transaction_details(&transaction);
        Ok((transaction, signature, recent_blockhash))
    }

    /// 格式化并打印出交易的详细信息，用于调试
    fn log_transaction_details(&self, transaction: &Transaction) {
        if !tracing::enabled!(Level::DEBUG) {
            // 在INFO及以上级别直接跳过，避免不必要的字符串格式化开销
            return;
        }
        let message = &transaction.message;
        if message.instructions.is_empty() {
            warn!("交易中没有任何指令可供记录。");
            return;
        }
        let main_instruction = &message.instructions[0];

        debug!("--- [交易构建详情 (按执行顺序)] ---");
        debug!("> 签名者: {}", self.wallet.pubkey());
        debug!("> 最近区块哈希: {}", message.recent_blockhash);

        let program_id = message.account_keys[main_instruction.program_id_index as usize];
        debug!("> 程序 ID: {}", program_id);
        
        // 注意：这里的账户列表是指令引用的账户，而不是消息中的所有账户
        debug!("> 账户列表 ({} - 按指令顺序):", main_instruction.accounts.len());

        for (i, account_index) in main_instruction.accounts.iter().enumerate() {
            let key_index = *account_index as usize;
             if let Some(key) = message.account_keys.get(key_index) {
                let is_signer = message.is_signer(key_index);
                let is_writable = message.is_writable(key_index);
                let signer_str = if is_signer { "[签名者]" } else { "" };
                let writable_str = if is_writable { "[可写]" } else { "[只读]" };
                debug!(
                    "  [{}] {} {} {}",
                    i, key, signer_str, writable_str
                );
            }
        }
        
        debug!("> 指令数据 (Hex): {}", hex::encode(&main_instruction.data));
        debug!("--- [详情结束] ---");
    }

    fn build_instruction_data(&self, target_tokens: u64, max_sol_cost: u64, is_buy: bool) -> Vec<u8> {
        let mut data = Vec::with_capacity(24);
        if is_buy {
            data.extend_from_slice(&BUY_DISCRIMINATOR);
            // pump.fun buy指令格式（Exact-Output）：
            // 第一个参数：amount - 目标购买的代币数量
            data.extend_from_slice(&target_tokens.to_le_bytes());
            // 第二个参数：max_sol_cost - 最大愿意花费的SOL
            data.extend_from_slice(&max_sol_cost.to_le_bytes());
        } else {
            data.extend_from_slice(&SELL_DISCRIMINATOR);
            data.extend_from_slice(&target_tokens.to_le_bytes()); // token_amount_in for sells
            data.extend_from_slice(&max_sol_cost.to_le_bytes()); // min_sol_out for sells
        }
        data
    }

    pub fn build_unsigned_sell_transaction(
        &self,
        trade_info: &HotPathTrade,
        token_amount_in: u64,
        min_sol_out_lamports: u64,
        wallet_config: &WalletConfig,
        close_ata: bool,
        tip_account: Option<Pubkey>,
        accelerator_provider: Option<&str>,
    ) -> Result<Transaction, anyhow::Error> {

        let (template, main_ix_idx, tip_ix_idx, fee_ix_idx, compute_limit_ix_idx, compute_price_ix_idx, mint_idx, bc_idx, assoc_bc_idx, user_ata_idx, creator_vault_idx, tip_receiver_idx, fee_receiver_idx) = if close_ata {
            (&self.sell_close_template, self.sell_close_main_ix_idx, self.sell_close_tip_ix_idx, self.sell_close_fee_ix_idx, self.sell_close_compute_limit_ix_idx, self.sell_close_compute_price_ix_idx, self.sell_close_mint_key_idx, self.sell_close_bonding_curve_key_idx, self.sell_close_assoc_bc_key_idx, self.sell_close_user_ata_key_idx, self.sell_close_creator_vault_key_idx, self.sell_close_tip_receiver_key_idx, self.sell_close_fee_receiver_key_idx)
        } else {
            (&self.sell_template, self.sell_main_ix_idx, self.sell_tip_ix_idx, self.sell_fee_ix_idx, self.sell_compute_limit_ix_idx, self.sell_compute_price_ix_idx, self.sell_mint_key_idx, self.sell_bonding_curve_key_idx, self.sell_assoc_bc_key_idx, self.sell_user_ata_key_idx, self.sell_creator_vault_key_idx, self.sell_tip_receiver_key_idx, self.sell_fee_receiver_key_idx)
        };

        let mut message = template.clone();

        // 1. Overwrite dynamic accounts
        let keys = &mut message.account_keys;
        keys[mint_idx as usize] = trade_info.mint_pubkey;
        keys[bc_idx as usize] = trade_info.bonding_curve_pubkey;
        keys[assoc_bc_idx as usize] = trade_info.associated_bonding_curve;
        keys[user_ata_idx as usize] = trade_info.user_ata;
        keys[creator_vault_idx as usize] = trade_info.creator_vault_pubkey;
        if let Some(tip_acc) = tip_account {
             keys[tip_receiver_idx as usize] = tip_acc;
        }
        
        // 设置手续费接收者地址（如果启用）
        if self.fee_config.enabled {
            if let Some(fee_receiver_pubkey) = self.fee_config.fee_pubkey {
                keys[fee_receiver_idx as usize] = fee_receiver_pubkey;
            }
        }

        // 2. Overwrite instruction data
        let main_ix = &mut message.instructions[main_ix_idx];
        main_ix.data = self.build_instruction_data(token_amount_in, min_sol_out_lamports, false);
        
        if tip_account.is_some() {
            let tip_ix = &mut message.instructions[tip_ix_idx];
            // 取卖出专用 tip 百分比；若为空则回退到通用 accelerator_tip_percentage，再默认 1%
            let tip_pct = wallet_config
                .sell_tip_percentage
                .or(wallet_config.accelerator_tip_percentage)
                .unwrap_or(1.0);
            let tip_lamports = ((min_sol_out_lamports as f64) * (tip_pct / 100.0)) as u64;
            // 根据加速器类型获取最低tip限制
            let min_tip = get_min_tip_lamports_for_accelerator(accelerator_provider.unwrap_or(""));
            let final_tip_lamports = tip_lamports.max(min_tip);
            tip_ix.data = create_transfer_data(final_tip_lamports);
        }

        // 2.2. 处理手续费指令（零延迟，从内存配置读取）
        if self.fee_config.enabled {
            let fee_ix = &mut message.instructions[fee_ix_idx];
            let fee_lamports = ((min_sol_out_lamports as f64) * self.fee_config.fee_percentage) as u64;
            fee_ix.data = create_transfer_data(fee_lamports);
            debug!("卖出手续费: {} lamports ({}%)", fee_lamports, self.fee_config.fee_percentage * 100.0);
        }
        
        // 2.1. Update compute budget instructions with wallet config values
        let compute_limit_ix = &mut message.instructions[compute_limit_ix_idx];
        // 卖出专用 compute unit limit 优先
        let effective_compute_limit = wallet_config
            .sell_compute_unit_limit
            .unwrap_or(wallet_config.compute_unit_limit);
        let compute_limit = if effective_compute_limit > 0 {
            effective_compute_limit
        } else {
            80_000
        };
        let new_limit_instruction = ComputeBudgetInstruction::set_compute_unit_limit(compute_limit);
        compute_limit_ix.data = new_limit_instruction.data;
        
        let compute_price_ix = &mut message.instructions[compute_price_ix_idx];
        // 优先使用卖出专用 priority_fee，如未设置则回退到通用 priority_fee
        let effective_priority_fee = wallet_config
            .sell_priority_fee
            .unwrap_or(wallet_config.priority_fee);
        let compute_price = if effective_priority_fee > 0 {
            effective_priority_fee
        } else {
            95_000
        };
        let new_price_instruction = ComputeBudgetInstruction::set_compute_unit_price(compute_price);
        compute_price_ix.data = new_price_instruction.data;
        
        // 3. Set the recent blockhash
        message.recent_blockhash = self
            .blockhash_service
            .get_blockhash_with_lag(self.blockhash_lag_slots)
            .map(|d| d.hash)
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;

        Ok(Transaction::new_unsigned(message))
    }

    /// 获取最新区块哈希
    pub async fn get_recent_blockhash(&self) -> Result<Hash> {
        self.blockhash_service
            .get_blockhash_with_lag(self.blockhash_lag_slots)
            .map(|d| d.hash)
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))
    }

    /// 从指令列表创建交易
    pub async fn create_transaction_from_instructions(
        &self, 
        instructions: Vec<Instruction>,
        memo: Option<String>
    ) -> Result<(Transaction, Signature)> {
        // 获取最新区块哈希
        let blockhash = self.get_recent_blockhash().await?;

        // 创建完整的交易
        let mut tx = Transaction::new_with_payer(&instructions, Some(&self.get_wallet_pubkey()));

        // 如果有备注信息，直接添加到交易数据中
        if let Some(memo_text) = memo {
            // 这里简化处理，不添加memo指令，而是保留在日志中
            debug!("交易备注: {}", memo_text);
        }

        // 设置区块哈希
        tx.message.recent_blockhash = blockhash;

        // 签名交易
        tx.sign(&[self.wallet.as_ref()], tx.message.recent_blockhash);

        // 获取签名
        let signature = tx.signatures[0];

        Ok((tx, signature))
    }

    // --- Template Creation Helpers ---

    fn create_buy_template(wallet_pubkey: &Pubkey, fee_config: &FeeConfig) -> (Message, usize, usize, usize, usize, usize, u8, u8, u8, u8, u8, u8, u8) {
        let p = || Pubkey::new_unique(); // Placeholder generator
        let (mint_p, bc_p, assoc_bc_p, creator_vault_p, tip_receiver_p, fee_receiver_p) = (p(), p(), p(), p(), p(), p());

        // Parse fee receiver address from config
        let fee_receiver_pubkey = if fee_config.enabled {
            fee_config.fee_pubkey.unwrap_or_else(|| fee_receiver_p)
        } else {
            fee_receiver_p // use placeholder if disabled
        };

        // Derive the associated token account address from the mint placeholder.
        // This is crucial for ensuring the seed derivation is correct in the template.
        let user_ata_p = spl_associated_token_account::get_associated_token_address(wallet_pubkey, &mint_p);

        // 💫 创建 globalVolumeAccumulator 和 userVolumeAccumulator PDA 地址
        // 使用IDL中定义的确切种子字符串
        let (global_volume_accumulator, _) = Pubkey::find_program_address(
            &[b"global_volume_accumulator"],
            &PUMP_FUN_PROGRAM_ID,
        );
        let (user_volume_accumulator, _) = Pubkey::find_program_address(
            &[b"user_volume_accumulator", wallet_pubkey.as_ref()],
            &PUMP_FUN_PROGRAM_ID,
        );

        // Instruction 1: Idempotent ATA Creation (Standard Library)
        let create_ata_ix = spl_associated_token_account::instruction::create_associated_token_account_idempotent(
            wallet_pubkey,      // Payer
            wallet_pubkey,      // Wallet address for ATA
            &mint_p,            // Mint
            &TOKEN_PROGRAM_ID,  // Token Program ID
        );

        // Instruction 2: Main Pump.fun Buy
        let main_ix = Instruction {
            program_id: PUMP_FUN_PROGRAM_ID,
            accounts: vec![
                AccountMeta::new_readonly(GLOBAL_ACCOUNT, false),
                AccountMeta::new(FEE_RECIPIENT, false),
                AccountMeta::new_readonly(mint_p, false),
                AccountMeta::new(bc_p, false),
                AccountMeta::new(assoc_bc_p, false),
                AccountMeta::new(user_ata_p, false), // Use the derived placeholder
                AccountMeta::new(*wallet_pubkey, true),
                AccountMeta::new_readonly(SYSTEM_PROGRAM_ID, false),
                AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),
                AccountMeta::new(creator_vault_p, false),
                AccountMeta::new_readonly(EVENT_AUTHORITY, false),
                AccountMeta::new_readonly(PUMP_FUN_PROGRAM_ID, false),
                // 💫 新增的 volume accumulator 账户
                AccountMeta::new(global_volume_accumulator, false),
                AccountMeta::new(user_volume_accumulator, false),
            ],
            data: vec![], // Will be replaced
        };

        // Instruction 3: Tip
        let tip_ix = system_instruction::transfer(wallet_pubkey, &tip_receiver_p, 0); // Amount will be replaced

        // Instruction 4: Fee (only if enabled)
        let fee_ix = system_instruction::transfer(wallet_pubkey, &fee_receiver_pubkey, 0); // Amount will be replaced

        // Full instruction set for the template
        let mut instructions = vec![
            ComputeBudgetInstruction::set_compute_unit_limit(80_000),
            ComputeBudgetInstruction::set_compute_unit_price(95_000),
            create_ata_ix,
            main_ix.clone(),
            tip_ix,
        ];

        // Add fee instruction if enabled
        if fee_config.enabled {
            instructions.push(fee_ix);
        }

        let message = Message::new_with_blockhash(&instructions, Some(wallet_pubkey), &Hash::new_unique());

        let main_ix_idx = instructions.iter().position(|ix| ix.program_id == PUMP_FUN_PROGRAM_ID).unwrap();
        
        // Find tip instruction index (first system transfer after main instruction)
        let tip_ix_idx = instructions.iter()
            .position(|ix| ix.program_id == SYSTEM_PROGRAM_ID && ix.accounts.len() == 2 && ix.accounts[1].pubkey == tip_receiver_p)
            .unwrap();
        
        // Find fee instruction index (second system transfer if enabled)
        let fee_ix_idx = if fee_config.enabled {
            instructions.iter()
                .position(|ix| ix.program_id == SYSTEM_PROGRAM_ID && ix.accounts.len() == 2 && ix.accounts[1].pubkey == fee_receiver_pubkey)
                .unwrap()
        } else {
            0 // placeholder, won't be used
        };

        let compute_limit_ix_idx = 0; // First instruction is always compute unit limit
        let compute_price_ix_idx = 1; // Second instruction is always compute unit price

        let keys = &message.account_keys;
        let mint_key_idx = keys.iter().position(|k| *k == mint_p).unwrap() as u8;
        let bonding_curve_key_idx = keys.iter().position(|k| *k == bc_p).unwrap() as u8;
        let assoc_bc_key_idx = keys.iter().position(|k| *k == assoc_bc_p).unwrap() as u8;
        let user_ata_key_idx = keys.iter().position(|k| *k == user_ata_p).unwrap() as u8;
        let creator_vault_key_idx = keys.iter().position(|k| *k == creator_vault_p).unwrap() as u8;
        let tip_receiver_key_idx = keys.iter().position(|k| *k == tip_receiver_p).unwrap() as u8;
        let fee_receiver_key_idx = keys.iter().position(|k| *k == fee_receiver_pubkey).unwrap() as u8;

        (message, main_ix_idx, tip_ix_idx, fee_ix_idx, compute_limit_ix_idx, compute_price_ix_idx, mint_key_idx, bonding_curve_key_idx, assoc_bc_key_idx, user_ata_key_idx, creator_vault_key_idx, tip_receiver_key_idx, fee_receiver_key_idx)
    }

    fn create_sell_template(wallet_pubkey: &Pubkey, fee_config: &FeeConfig, include_close: bool) -> (Message, usize, usize, usize, usize, usize, u8, u8, u8, u8, u8, u8, u8) {
        let p = || Pubkey::new_unique();
        let (mint_p, bc_p, assoc_bc_p, user_ata_p, creator_vault_p, tip_receiver_p, fee_receiver_p) = (p(), p(), p(), p(), p(), p(), p());

        // Parse fee receiver address from config
        let fee_receiver_pubkey = if fee_config.enabled {
            fee_config.fee_pubkey.unwrap_or_else(|| fee_receiver_p)
        } else {
            fee_receiver_p // use placeholder if disabled
        };

        let main_ix = Instruction {
            program_id: PUMP_FUN_PROGRAM_ID,
            accounts: vec![
                AccountMeta::new_readonly(GLOBAL_ACCOUNT, false),
                AccountMeta::new(FEE_RECIPIENT, false),
                AccountMeta::new_readonly(mint_p, false),
                AccountMeta::new(bc_p, false),
                AccountMeta::new(assoc_bc_p, false),
                AccountMeta::new(user_ata_p, false),
                AccountMeta::new(*wallet_pubkey, true),
                AccountMeta::new_readonly(SYSTEM_PROGRAM_ID, false),
                AccountMeta::new(creator_vault_p, false),
                AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),
                AccountMeta::new_readonly(EVENT_AUTHORITY, false),
                AccountMeta::new_readonly(PUMP_FUN_PROGRAM_ID, false),
            ],
            data: vec![],
        };

        let tip_ix = system_instruction::transfer(wallet_pubkey, &tip_receiver_p, 0);
        
        // Fee instruction (only if enabled)
        let fee_ix = system_instruction::transfer(wallet_pubkey, &fee_receiver_pubkey, 0);

        let mut instructions = vec![
            ComputeBudgetInstruction::set_compute_unit_limit(80_000),
            ComputeBudgetInstruction::set_compute_unit_price(95_000),
            main_ix.clone(),
        ];

        if include_close {
            instructions.push(spl_token::instruction::close_account(
                &TOKEN_PROGRAM_ID,
                &user_ata_p,
                wallet_pubkey,
                wallet_pubkey,
                &[],
            ).unwrap());
        }

        // Tip instruction must be the last one for accelerators like Oslot
        instructions.push(tip_ix);
        
        // Add fee instruction if enabled
        if fee_config.enabled {
            instructions.push(fee_ix);
        }

        let message = Message::new_with_blockhash(&instructions, Some(wallet_pubkey), &Hash::new_unique());

        let main_ix_idx = instructions.iter().position(|ix| ix.program_id == PUMP_FUN_PROGRAM_ID).unwrap();
        
        // Find tip instruction index (first system transfer)
        let tip_ix_idx = instructions.iter()
            .position(|ix| ix.program_id == SYSTEM_PROGRAM_ID && ix.accounts.len() == 2 && ix.accounts[1].pubkey == tip_receiver_p)
            .unwrap();
        
        // Find fee instruction index (second system transfer if enabled)
        let fee_ix_idx = if fee_config.enabled {
            instructions.iter()
                .position(|ix| ix.program_id == SYSTEM_PROGRAM_ID && ix.accounts.len() == 2 && ix.accounts[1].pubkey == fee_receiver_pubkey)
                .unwrap()
        } else {
            0 // placeholder, won't be used
        };

        let compute_limit_ix_idx = 0; // First instruction is always compute unit limit
        let compute_price_ix_idx = 1; // Second instruction is always compute unit price

        let keys = &message.account_keys;
        let mint_key_idx = keys.iter().position(|k| *k == mint_p).unwrap() as u8;
        let bonding_curve_key_idx = keys.iter().position(|k| *k == bc_p).unwrap() as u8;
        let assoc_bc_key_idx = keys.iter().position(|k| *k == assoc_bc_p).unwrap() as u8;
        let user_ata_key_idx = keys.iter().position(|k| *k == user_ata_p).unwrap() as u8;
        let creator_vault_key_idx = keys.iter().position(|k| *k == creator_vault_p).unwrap() as u8;
        let tip_receiver_key_idx = keys.iter().position(|k| *k == tip_receiver_p).unwrap() as u8;
        let fee_receiver_key_idx = keys.iter().position(|k| *k == fee_receiver_pubkey).unwrap() as u8;

        (message, main_ix_idx, tip_ix_idx, fee_ix_idx, compute_limit_ix_idx, compute_price_ix_idx, mint_key_idx, bonding_curve_key_idx, assoc_bc_key_idx, user_ata_key_idx, creator_vault_key_idx, tip_receiver_key_idx, fee_receiver_key_idx)
    }
}
