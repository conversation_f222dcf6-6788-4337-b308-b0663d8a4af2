use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    system_instruction,
    compute_budget::ComputeBudgetInstruction,
    message::Message,
    signature::{Keypair, Signer},
    hash::Hash,
};
use spl_token::ID as TOKEN_PROGRAM_ID;
use std::{str::FromStr, sync::Arc};
use crate::shared::types::{WalletConfig, HotPathTrade};
use crate::blockhash_service::BlockhashService;
use tracing::{info, debug, warn};
use anyhow::{anyhow, Result};

// 🚀 预计算常量 - 启动时解析一次，运行时零开销访问
pub const RAYDIUM_LAUNCHPAD_PROGRAM_ID: &str = "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj";
pub const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";

// 🚀 编译时常量 - 零运行时开销
const RAYDIUM_PROGRAM_PUBKEY: Pubkey = solana_sdk::pubkey!("LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj");
const WSOL_MINT_PUBKEY: Pubkey = solana_sdk::pubkey!("So11111111111111111111111111111111111111112");
const GLOBAL_CONFIG_PUBKEY: Pubkey = solana_sdk::pubkey!("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX");
const PLATFORM_CONFIG_PUBKEY: Pubkey = solana_sdk::pubkey!("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1");

// 🚀 指令数据常量 - 预编译字节数组
const RAYDIUM_BUY_DISCRIMINATOR: [u8; 8] = [250, 234, 13, 123, 213, 156, 19, 236];
const SYSTEM_TRANSFER_DISCRIMINATOR: [u8; 4] = [2, 0, 0, 0];

/// 🚀 预计算PDA结构 - 启动时计算一次，运行时直接使用
#[derive(Clone)]
struct PrecomputedPDAs {
    authority: Pubkey,       // vault_auth_seed PDA
    event_authority: Pubkey, // __event_authority PDA
}

/// 根据加速器类型获取最低tip限制 (与pump协议保持一致)
fn get_min_tip_lamports(accelerator_provider: &str) -> u64 {
    match accelerator_provider {
        "astralane" => 10_000,       // Astralane: 0.00001 SOL (官方要求)
        "blockrazor" => 1_000_000,   // BlockRazor: 0.001 SOL (官方要求)
        "oslot" => 100_000,          // Oslot: 0.0001 SOL
        "flashblock" => 100_000,     // Flashblock: 0.0001 SOL (与Oslot保持一致)
        _ => 100_000,                // 默认: 0.0001 SOL
    }
}

/// 🚀 Bonk高性能构建器 - 专为Raydium协议优化的无状态构建系统
/// 基于Pump协议优化原理，完全线程安全，无需锁保护
pub struct BonkTransactionBuilder {
    // 核心组件 - 只读，线程安全
    wallet: Arc<Keypair>,
    blockhash_service: Arc<BlockhashService>,
    blockhash_lag_slots: usize,

    // 🚀 预计算优化组件 - 启动时计算一次，运行时零开销
    precomputed_pdas: PrecomputedPDAs,     // PDA地址预计算
}

impl BonkTransactionBuilder {
    /// 🚀 创建高性能Bonk构建器 - 预计算所有可预计算的组件
    /// 基于Pump协议优化原理，完全无状态，线程安全
    pub fn new(
        wallet: Arc<Keypair>,
        blockhash_service: Arc<BlockhashService>,
        blockhash_lag_slots: usize,
        _compute_unit_limit: u32,  // 保持接口兼容
        _priority_fee: u64,        // 保持接口兼容
    ) -> Self {
        // 🚀 预计算所有PDA地址 - 启动时计算一次，运行时零开销
        let precomputed_pdas = Self::precompute_pdas();

        Self {
            wallet,
            blockhash_service,
            blockhash_lag_slots,
            precomputed_pdas,
        }
    }

    /// 🚀 预计算PDA地址 - 启动时执行一次
    fn precompute_pdas() -> PrecomputedPDAs {
        let authority_seed = b"vault_auth_seed";
        let event_authority_seed = b"__event_authority";
        
        let (authority, _) = Pubkey::find_program_address(&[authority_seed], &RAYDIUM_PROGRAM_PUBKEY);
        let (event_authority, _) = Pubkey::find_program_address(&[event_authority_seed], &RAYDIUM_PROGRAM_PUBKEY);
        
        PrecomputedPDAs {
            authority,
            event_authority,
        }
    }

    /// 获取钱包公钥 (保持接口兼容)
    pub fn get_wallet_pubkey(&self) -> Pubkey {
        self.wallet.pubkey()
    }

    /// 🚀 构建4个不同tip地址的变种交易 - 复用所有计算，仅替换tip地址
    pub fn build_multi_tip_transactions(
        &self,
        trade: &HotPathTrade,
        buy_amount_sol: f64,
        config: &WalletConfig,
    ) -> Result<Vec<(solana_sdk::transaction::Transaction, String)>> {
        use crate::services::senders::{astralane_sender::AstralaneSender, blockrazor_sender::BlockRazorSender, flashblock_sender::FlashblockSender, oslot_sender::OslotSender};
        use std::str::FromStr;
        use crate::services::nonce_helper::{get_transaction_blockhash, is_using_nonce};
        use crate::services::nonce_pool::NonceAccountPool;
        
        // 🚀 第一步：预先获取共享的nonce账户（只获取一次）
        let using_nonce = is_using_nonce();
        let shared_nonce_info = if using_nonce {
            let pool = NonceAccountPool::get_instance();
            pool.get_next_available_account()
        } else {
            None
        };
        
        // 如果使用nonce，立即标记为已使用，防止其他交易重复使用
        if let Some(ref nonce_info) = shared_nonce_info {
            let pool = NonceAccountPool::get_instance();
            pool.release_account(&nonce_info.address);
            info!("🔒 预先标记共享nonce账户为已使用: {}", nonce_info.address);
        }
        
        // 定义四个加速器的tip地址
        let accelerators = vec![
            ("astralane", AstralaneSender::get_random_tip_account()),
            ("blockrazor", BlockRazorSender::get_random_tip_account()),
            ("oslot", OslotSender::get_random_tip_account()),
            ("flashblock", FlashblockSender::get_random_tip_account()),
        ];
        
        let mut transactions = Vec::with_capacity(4);
        
        // 🚀 第二步：为每个加速器构建变种交易，使用共享nonce
        for (provider, tip_address) in accelerators {
            let tip_pubkey = match Pubkey::from_str(tip_address) {
                Ok(pubkey) => Some(pubkey),
                Err(_) => {
                    warn!("无效的tip地址: {} for {}, 跳过", tip_address, provider);
                    continue;
                }
            };
            
            // 🚀 构建单个变种交易，强制使用共享nonce
            let transaction = self.build_single_transaction_with_shared_nonce(
                trade,
                buy_amount_sol,
                config,
                tip_pubkey,
                Some(provider),
                shared_nonce_info.as_ref(),
            )?;
            
            transactions.push((transaction, provider.to_string()));
        }
        
        info!("🚀 成功构建{}个变种交易，共享nonce: {:?}", 
              transactions.len(), 
              shared_nonce_info.as_ref().map(|n| n.current_nonce.to_string()));
        
        Ok(transactions)
    }

    /// 🚀 内部方法：构建单个交易，使用预先提供的共享nonce
    fn build_single_transaction_with_shared_nonce(
        &self,
        trade: &HotPathTrade,
        buy_amount_sol: f64,
        config: &WalletConfig,
        tip_account: Option<Pubkey>,
        accelerator_provider: Option<&str>,
        shared_nonce_info: Option<&crate::services::nonce_pool::NonceAccountInfo>,
    ) -> Result<solana_sdk::transaction::Transaction> {
        let user_pubkey = self.wallet.pubkey();
        
        // --- 🚀 快速参数计算 (复用原有逻辑) ---
        let price_to_use = trade.price;
        
        let target_token_amount = match config.follow_mode {
            crate::shared::types::FollowMode::Percentage => {
                if let Some(pct) = config.follow_percentage {
                    // trade.token_amount 已经是raw units，直接按百分比计算
                    trade.token_amount as f64 * (pct / 100.0)
                } else {
                    // fallback：基于SOL金额反推token数量，转换为raw units
                    buy_amount_sol / price_to_use * 1_000_000.0
                }
            },
            crate::shared::types::FollowMode::FixedAmount => {
                // 固定金额跟单：基于用户设定的SOL金额反推token数量，转换为raw units
                buy_amount_sol / price_to_use * 1_000_000.0
            }
        };
        
        let target_token_amount_ui = target_token_amount / 1_000_000.0;
        let required_sol = target_token_amount_ui * price_to_use;
        let max_sol_cost_lamports = (required_sol * 1_000_000_000.0 * (1.0 + config.slippage_percentage / 100.0)) as u64;
        let target_token_amount_raw = target_token_amount as u64;

        // --- 🚀 构建指令 ---
        let user_wsol_account = spl_associated_token_account::get_associated_token_address(&user_pubkey, &WSOL_MINT_PUBKEY);
        let user_token_account = spl_associated_token_account::get_associated_token_address(&user_pubkey, &trade.mint_pubkey);
        
        let instructions = self.build_instructions_stateless(
            &user_pubkey,
            &user_wsol_account, 
            &user_token_account,
            trade,
            config,
            max_sol_cost_lamports,
            target_token_amount_raw,
            tip_account,
            accelerator_provider,
        )?;

        // --- 🚀 构建交易：优先使用共享nonce ---
        if let Some(nonce_info) = shared_nonce_info {
            info!("🎯 使用共享nonce构建{}变种交易: {} (nonce: {})", 
                  accelerator_provider.unwrap_or("unknown"), 
                  nonce_info.address, 
                  nonce_info.current_nonce);
            
            // 创建advance nonce指令
            let advance_nonce_ix = system_instruction::advance_nonce_account(
                &nonce_info.address,
                &self.wallet.pubkey(),
            );
            
            // 重新构建包含nonce指令的指令列表
            let mut all_instructions = vec![advance_nonce_ix];
            all_instructions.extend(instructions);
            
            // 使用nonce值作为blockhash构建交易
            let message = Message::new(&all_instructions, Some(&user_pubkey));
            let mut transaction = solana_sdk::transaction::Transaction::new_unsigned(message);
            transaction.message.recent_blockhash = nonce_info.current_nonce;
            
            return Ok(transaction);
        }
        
        // 回退到普通区块哈希交易
        let recent_blockhash = self
            .blockhash_service
            .get_blockhash_with_lag(self.blockhash_lag_slots)
            .map(|d| d.hash)
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;

        let message = Message::new(&instructions, Some(&user_pubkey));
        let mut transaction = solana_sdk::transaction::Transaction::new_unsigned(message);
        transaction.message.recent_blockhash = recent_blockhash;
        
        Ok(transaction)
    }

    /// 🚀 无状态高性能买入交易构建 - 基于Pump优化原理，完全线程安全
    /// 目标性能: < 0.1ms构建时间，无锁访问，线程安全
    #[inline(always)]
    pub fn build_buy_transaction(
        &self, // 🚀 只读借用，完全线程安全
        trade: &HotPathTrade,
        buy_amount_sol: f64,
        config: &WalletConfig,
        tip_account: Option<Pubkey>,
        accelerator_provider: Option<&str>,
    ) -> Result<solana_sdk::transaction::Transaction> {
        let user_pubkey = self.wallet.pubkey();
        
        // --- 🚀 快速参数计算 (内联优化) - 避免RwLock竞争 ---
        let price_to_use = trade.price; // 直接使用交易价格，避免全局锁竞争
        
        // 统一逻辑：先确定目标token数量，再计算max_sol_cost和minimum_amount_out
        let target_token_amount = match config.follow_mode {
            crate::shared::types::FollowMode::Percentage => {
                // 百分比跟单：trade.token_amount 已经是raw units，直接按百分比计算
                if let Some(pct) = config.follow_percentage {
                    trade.token_amount as f64 * (pct / 100.0)
                } else {
                    // fallback：基于SOL金额反推token数量，转换为raw units
                    buy_amount_sol / price_to_use * 1_000_000.0
                }
            },
            crate::shared::types::FollowMode::FixedAmount => {
                // 固定金额跟单：基于用户设定的SOL金额反推token数量，转换为raw units
                buy_amount_sol / price_to_use * 1_000_000.0
            }
        };
        
        // 根据目标token数量计算max_sol_cost（加滑点）
        // target_token_amount是raw units，price_to_use是SOL per token (UI)，需要转换
        let target_token_amount_ui = target_token_amount / 1_000_000.0;
        let required_sol = target_token_amount_ui * price_to_use;
        let max_sol_cost_lamports = (required_sol * 1_000_000_000.0 * (1.0 + config.slippage_percentage / 100.0)) as u64;
        
        // minimum_amount_out就是目标token数量（用于滑点保护）
        let target_token_amount_raw = target_token_amount as u64;

        // --- 🚀 无状态构建流水线 - 栈上分配，线程安全 ---
        
        // 地址计算 (直接计算，无缓存)
        let user_wsol_account = spl_associated_token_account::get_associated_token_address(&user_pubkey, &WSOL_MINT_PUBKEY);
        let user_token_account = spl_associated_token_account::get_associated_token_address(&user_pubkey, &trade.mint_pubkey);
        
        // 构建指令列表 (栈上分配)
        let instructions = self.build_instructions_stateless(
            &user_pubkey,
            &user_wsol_account, 
            &user_token_account,
            trade,
            config,
            max_sol_cost_lamports,
            target_token_amount_raw,
            tip_account,
            accelerator_provider,
        )?;

        // 构建交易 - 🚀 使用nonce构建交易
        use crate::services::nonce_helper::{get_transaction_blockhash, is_using_nonce};
        use crate::services::nonce_pool::NonceAccountPool;
        
        let using_nonce = is_using_nonce();
        info!("🔍 检查Bonk nonce状态: using_nonce={}", using_nonce);
        
        if using_nonce {
            info!("🚀 开始构建Bonk nonce交易");
            
            // 获取nonce账户信息
            let pool = NonceAccountPool::get_instance();
            if let Some(nonce_account_info) = pool.get_next_available_account() {
                info!("🎯 Bonk使用nonce账户: {} (nonce: {})", nonce_account_info.address, nonce_account_info.current_nonce);
                
                // 创建advance nonce指令
                let advance_nonce_ix = system_instruction::advance_nonce_account(
                    &nonce_account_info.address,
                    &self.wallet.pubkey(),
                );
                
                // 重新构建包含nonce指令的指令列表
                let mut all_instructions = vec![advance_nonce_ix];
                all_instructions.extend(instructions);
                
                // 使用nonce值作为blockhash构建交易
                let message = Message::new(&all_instructions, Some(&user_pubkey));
                let mut transaction = solana_sdk::transaction::Transaction::new_unsigned(message);
                transaction.message.recent_blockhash = nonce_account_info.current_nonce;
                
                info!("🚀 成功构建Bonk nonce交易，包含{}个指令", all_instructions.len());
                
                // 🚀 立即标记nonce账户为已使用
                pool.release_account(&nonce_account_info.address);
                info!("🔒 标记Bonk nonce账户为已使用: {}", nonce_account_info.address);
                
                return Ok(transaction);
            } else {
                info!("⚠️ 无可用nonce账户，Bonk交易使用普通区块哈希");
            }
        }
        
        // 回退到普通区块哈希交易
        let recent_blockhash = self
            .blockhash_service
            .get_blockhash_with_lag(self.blockhash_lag_slots)
            .map(|d| d.hash)
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;

        let message = Message::new(&instructions, Some(&user_pubkey));
        let mut transaction = solana_sdk::transaction::Transaction::new_unsigned(message);
        transaction.message.recent_blockhash = recent_blockhash;
        
        info!("🔄 Bonk使用普通区块哈希交易: {}", recent_blockhash);
        
        Ok(transaction)
    }

    /// 🚀 无状态指令构建 - 线程安全，栈上分配
    #[inline(always)]
    fn build_instructions_stateless(
        &self,
        user_pubkey: &Pubkey,
        user_wsol_account: &Pubkey,
        user_token_account: &Pubkey,
        trade: &HotPathTrade,
        config: &WalletConfig,
        max_sol_cost_lamports: u64,
        target_token_amount_raw: u64,
        tip_account: Option<Pubkey>,
        accelerator_provider: Option<&str>,
    ) -> Result<Vec<Instruction>> {
        let mut instructions = Vec::with_capacity(9); // 预分配容量
        
        // 构建交换数据
        let swap_instruction = self.build_swap_instruction_stateless(
            user_pubkey,
            user_wsol_account,
            user_token_account,
            trade,
            max_sol_cost_lamports,
            target_token_amount_raw,
        )?;
        
        // 🚀 批量构建所有指令
        instructions.extend_from_slice(&[
            // 1. 计算预算指令
            ComputeBudgetInstruction::set_compute_unit_limit(config.compute_unit_limit),
            ComputeBudgetInstruction::set_compute_unit_price(config.priority_fee),
            
            // 2. 创建WSOL关联账户 (幂等)
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                user_pubkey, user_pubkey, &WSOL_MINT_PUBKEY, &TOKEN_PROGRAM_ID
            ),
            
            // 3. 创建目标Token关联账户 (幂等)
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                user_pubkey, user_pubkey, &trade.mint_pubkey, &TOKEN_PROGRAM_ID
            ),
            
            // 4. SOL转换为WSOL
            system_instruction::transfer(user_pubkey, user_wsol_account, max_sol_cost_lamports),
            
            // 5. 同步WSOL账户
            spl_token::instruction::sync_native(&TOKEN_PROGRAM_ID, user_wsol_account).unwrap(),
            
            // 6. 核心交换指令
            swap_instruction,
        ]);
        
        // 7. 可选: 关闭WSOL账户
        instructions.push(
            spl_token::instruction::close_account(
                &TOKEN_PROGRAM_ID,
                user_wsol_account,
                user_pubkey,
                user_pubkey,
                &[]
            ).unwrap()
        );
        
        // 8. 转账手续费指令 (参考pump实现)
        let fee_address = "Cd4magyg5n2Qs1dLZEoLfCRYKxHrnkf61RBr14PyVbHW"; // 与pump保持一致的手续费地址
        let fee_percentage = 0.004; // 0.4% 手续费
        if let Ok(fee_pubkey) = Pubkey::from_str(fee_address) {
            let fee_lamports = ((max_sol_cost_lamports as f64) * fee_percentage) as u64;
            if fee_lamports > 0 {
                instructions.push(
                    system_instruction::transfer(user_pubkey, &fee_pubkey, fee_lamports)
                );
            }
        }

        // 9. 可选: 加速器小费
        if let Some(tip_pubkey) = tip_account {
            // 先按百分比计算小费
            let tip_lamports = ((max_sol_cost_lamports as f64) * (config.accelerator_tip_percentage.unwrap_or(1.0) / 100.0)) as u64;
            // 根据加速器类型获取最低tip限制
            let min_tip = get_min_tip_lamports(accelerator_provider.unwrap_or(""));
            let final_tip_lamports = tip_lamports.max(min_tip);
            instructions.push(
                system_instruction::transfer(user_pubkey, &tip_pubkey, final_tip_lamports)
            );
        }
        
        Ok(instructions)
    }

    /// 🚀 无状态交换指令构建 - 线程安全
    #[inline(always)]
    fn build_swap_instruction_stateless(
        &self,
        user_pubkey: &Pubkey,
        user_wsol_account: &Pubkey,
        user_token_account: &Pubkey,
        trade: &HotPathTrade,
        max_sol_cost_lamports: u64,
        target_token_amount_raw: u64,
    ) -> Result<Instruction> {
        // 🚀 内联构建交换数据 - 栈上分配
        let mut swap_data = Vec::with_capacity(24);
        swap_data.extend_from_slice(&RAYDIUM_BUY_DISCRIMINATOR);
        swap_data.extend_from_slice(&max_sol_cost_lamports.to_le_bytes());
        swap_data.extend_from_slice(&target_token_amount_raw.to_le_bytes());
        swap_data.extend_from_slice(&0u64.to_le_bytes());

        // 🚀 正确的Raydium LaunchLab账户结构 - 基于成功模板
        let account_metas = vec![
            AccountMeta::new_readonly(*user_pubkey, true),                    // 0. payer
            AccountMeta::new_readonly(self.precomputed_pdas.authority, false), // 1. authority
            AccountMeta::new_readonly(GLOBAL_CONFIG_PUBKEY, false),           // 2. global_config
            AccountMeta::new_readonly(PLATFORM_CONFIG_PUBKEY, false),         // 3. platform_config
            AccountMeta::new(trade.creator_vault_pubkey, false),              // 4. pool_state
            AccountMeta::new(*user_token_account, false),                     // 5. user_base_token (Token账户)
            AccountMeta::new(*user_wsol_account, false),                      // 6. user_quote_token (WSOL账户)
            AccountMeta::new(trade.bonding_curve_pubkey, false),              // 7. base_vault
            AccountMeta::new(trade.associated_bonding_curve, false),          // 8. quote_vault
            AccountMeta::new_readonly(trade.mint_pubkey, false),              // 9. base_token_mint
            AccountMeta::new_readonly(WSOL_MINT_PUBKEY, false),               // 10. quote_token_mint
            AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),               // 11. base_token_program
            AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),               // 12. quote_token_program
            AccountMeta::new_readonly(self.precomputed_pdas.event_authority, false), // 13. event_authority
            AccountMeta::new_readonly(RAYDIUM_PROGRAM_PUBKEY, false),         // 14. program
        ];

        Ok(Instruction {
            program_id: RAYDIUM_PROGRAM_PUBKEY,
            accounts: account_metas,
            data: swap_data,
        })
    }

    /// 🚀 签名并记录详情 - 线程安全，无状态
    pub async fn sign_and_log_details(
        &self,
        mut transaction: solana_sdk::transaction::Transaction,
    ) -> Result<(solana_sdk::transaction::Transaction, solana_sdk::signature::Signature, Hash)> {
        // 🚀 关键修复：如果交易已经有nonce/blockhash设置，就不要重新获取
        let current_blockhash = transaction.message.recent_blockhash;
        let using_existing_blockhash = current_blockhash != Hash::default();
        
        let recent_blockhash = if using_existing_blockhash {
            // 使用已设置的nonce/blockhash（来自multi_tip_transactions）
            info!("🎯 使用预设的nonce/blockhash进行签名: {}", current_blockhash);
            current_blockhash
        } else {
            // 获取新的区块哈希（用于单独构建的交易）
            let new_blockhash = self
                .blockhash_service
                .get_blockhash_with_lag(self.blockhash_lag_slots)
                .map(|d| d.hash)
                .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;
            
            transaction.message.recent_blockhash = new_blockhash;
            new_blockhash
        };

        // 签名交易
        transaction.sign(&[self.wallet.as_ref()], recent_blockhash);
        let signature = transaction.signatures[0];

        debug!(
            "🚀 Bonk交易已签名: {} | 区块哈希: {} | 指令数: {} | 使用预设: {}",
            signature,
            recent_blockhash,
            transaction.message.instructions.len(),
            using_existing_blockhash
        );

        Ok((transaction, signature, recent_blockhash))
    }
}