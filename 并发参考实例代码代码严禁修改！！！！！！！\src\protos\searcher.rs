// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SlotList {
    #[prost(uint64, repeated, tag = "1")]
    pub slots: ::prost::alloc::vec::Vec<u64>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ConnectedLeadersResponse {
    /// Mapping of validator pubkey to leader slots for the current epoch.
    #[prost(map = "string, message", tag = "1")]
    pub connected_validators: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SlotList,
    >,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SendBundleRequest {
    #[prost(message, optional, tag = "1")]
    pub bundle: ::core::option::Option<super::bundle::Bundle>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SendBundleResponse {
    /// server uuid for the bundle
    #[prost(string, tag = "1")]
    pub uuid: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct NextScheduledLeaderRequest {
    /// Defaults to the currently connected region if no region provided.
    #[prost(string, repeated, tag = "1")]
    pub regions: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct NextScheduledLeaderResponse {
    /// the current slot the backend is on
    #[prost(uint64, tag = "1")]
    pub current_slot: u64,
    /// the slot of the next leader
    #[prost(uint64, tag = "2")]
    pub next_leader_slot: u64,
    /// the identity pubkey (base58) of the next leader
    #[prost(string, tag = "3")]
    pub next_leader_identity: ::prost::alloc::string::String,
    /// the block engine region of the next leader
    #[prost(string, tag = "4")]
    pub next_leader_region: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ConnectedLeadersRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ConnectedLeadersRegionedRequest {
    /// Defaults to the currently connected region if no region provided.
    #[prost(string, repeated, tag = "1")]
    pub regions: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ConnectedLeadersRegionedResponse {
    #[prost(map = "string, message", tag = "1")]
    pub connected_validators: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        ConnectedLeadersResponse,
    >,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetTipAccountsRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetTipAccountsResponse {
    #[prost(string, repeated, tag = "1")]
    pub accounts: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeBundleResultsRequest {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetRegionsRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetRegionsResponse {
    /// The region the client is currently connected to
    #[prost(string, tag = "1")]
    pub current_region: ::prost::alloc::string::String,
    /// Regions that are online and ready for connections
    /// All regions: <https://jito-labs.gitbook.io/mev/systems/connecting/mainnet>
    #[prost(string, repeated, tag = "2")]
    pub available_regions: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
/// Generated client implementations.
pub mod searcher_service_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct SearcherServiceClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl SearcherServiceClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> SearcherServiceClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> SearcherServiceClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            SearcherServiceClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        /// Searchers can invoke this endpoint to subscribe to their respective bundle results.
        /// A success result would indicate the bundle won its state auction and was submitted to the validator.
        pub async fn subscribe_bundle_results(
            &mut self,
            request: impl tonic::IntoRequest<super::SubscribeBundleResultsRequest>,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::super::bundle::BundleResult>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/searcher.SearcherService/SubscribeBundleResults",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("searcher.SearcherService", "SubscribeBundleResults"),
                );
            self.inner.server_streaming(req, path, codec).await
        }
        pub async fn send_bundle(
            &mut self,
            request: impl tonic::IntoRequest<super::SendBundleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::SendBundleResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/searcher.SearcherService/SendBundle",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("searcher.SearcherService", "SendBundle"));
            self.inner.unary(req, path, codec).await
        }
        /// Returns the next scheduled leader connected to the block engine.
        pub async fn get_next_scheduled_leader(
            &mut self,
            request: impl tonic::IntoRequest<super::NextScheduledLeaderRequest>,
        ) -> std::result::Result<
            tonic::Response<super::NextScheduledLeaderResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/searcher.SearcherService/GetNextScheduledLeader",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("searcher.SearcherService", "GetNextScheduledLeader"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Returns leader slots for connected jito validators during the current epoch. Only returns data for this region.
        pub async fn get_connected_leaders(
            &mut self,
            request: impl tonic::IntoRequest<super::ConnectedLeadersRequest>,
        ) -> std::result::Result<
            tonic::Response<super::ConnectedLeadersResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/searcher.SearcherService/GetConnectedLeaders",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("searcher.SearcherService", "GetConnectedLeaders"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Returns leader slots for connected jito validators during the current epoch.
        pub async fn get_connected_leaders_regioned(
            &mut self,
            request: impl tonic::IntoRequest<super::ConnectedLeadersRegionedRequest>,
        ) -> std::result::Result<
            tonic::Response<super::ConnectedLeadersRegionedResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/searcher.SearcherService/GetConnectedLeadersRegioned",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "searcher.SearcherService",
                        "GetConnectedLeadersRegioned",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Returns the tip accounts searchers shall transfer funds to for the leader to claim.
        pub async fn get_tip_accounts(
            &mut self,
            request: impl tonic::IntoRequest<super::GetTipAccountsRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetTipAccountsResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/searcher.SearcherService/GetTipAccounts",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("searcher.SearcherService", "GetTipAccounts"));
            self.inner.unary(req, path, codec).await
        }
        /// Returns region the client is directly connected to, along with all available regions
        pub async fn get_regions(
            &mut self,
            request: impl tonic::IntoRequest<super::GetRegionsRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetRegionsResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/searcher.SearcherService/GetRegions",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("searcher.SearcherService", "GetRegions"));
            self.inner.unary(req, path, codec).await
        }
    }
}
