// This file is @generated by prost-build.
#[derive(<PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct TraceShred {
    /// source region, one of: <https://jito-labs.gitbook.io/mev/systems/connecting/mainnet>
    #[prost(string, tag = "1")]
    pub region: ::prost::alloc::string::String,
    /// timestamp of creation
    #[prost(message, optional, tag = "2")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    /// monotonically increases, resets upon service restart
    #[prost(uint32, tag = "3")]
    pub seq_num: u32,
}
