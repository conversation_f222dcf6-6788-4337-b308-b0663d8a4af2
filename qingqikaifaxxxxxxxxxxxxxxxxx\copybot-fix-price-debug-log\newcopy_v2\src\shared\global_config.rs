use crate::config::AcceleratorConfig;
use once_cell::sync::OnceCell;
use std::sync::Mutex;

// 全局静态变量，用于存储加速器配置
static ACCELERATOR_CONFIG: OnceCell<AcceleratorConfig> = OnceCell::new();

// 临时 provider 覆盖，用于多加速器并发发送
static TEMP_PROVIDER_OVERRIDE: Mutex<Option<String>> = Mutex::new(None);

/// 初始化全局加速器配置。此函数应在程序启动时调用一次。
pub fn initialize_accelerator_config(config: AcceleratorConfig) {
    if ACCELERATOR_CONFIG.set(config).is_err() {
        // 这将在程序尝试第二次初始化时发生，理论上不应该出现
        panic!("全局加速器配置已初始化，无法再次设置！");
    }
}

/// 获取对全局加速器配置的引用。
/// 如果配置尚未初始化，将导致 panic。
pub fn get_accelerator_config() -> &'static AcceleratorConfig {
    ACCELERATOR_CONFIG.get().expect("全局加速器配置尚未初始化！")
}

/// 临时设置 provider（仅用于当前线程的下一次发送）
pub fn set_temp_provider(provider: String) {
    let mut temp_provider = TEMP_PROVIDER_OVERRIDE.lock().unwrap();
    *temp_provider = Some(provider);
}

/// 清除临时 provider 设置
pub fn clear_temp_provider() {
    let mut temp_provider = TEMP_PROVIDER_OVERRIDE.lock().unwrap();
    *temp_provider = None;
}

/// 获取当前有效的 provider（优先使用临时设置）
pub fn get_effective_provider() -> String {
    let temp_provider = TEMP_PROVIDER_OVERRIDE.lock().unwrap();
    if let Some(ref provider) = *temp_provider {
        provider.clone()
    } else {
        get_accelerator_config().provider.clone()
    }
}