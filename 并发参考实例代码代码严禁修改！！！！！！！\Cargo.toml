[package]
name = "sol-trade-sdk"
version = "0.3.3"
edition = "2021"
authors = ["<PERSON> <<EMAIL>>", "sgxiang <<EMAIL>>", "wei <<EMAIL>>"]
repository = "https://github.com/0xfnzero/sol-trade-sdk"
description = "Rust SDK to interact with the dex trade Solana program."
license = "MIT"
keywords = ["solana", "memecoins", "pumpfun", "pumpswap", "raydium"]
readme = "README.md"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
solana-streamer-sdk = "0.2.4"
solana-sdk = "2.3.0"
solana-client = "2.3.6"
solana-program = "2.3.0" 
solana-rpc-client = "2.3.6"
solana-rpc-client-api = "2.3.6"
solana-transaction-status = "2.3.6"
solana-account-decoder = "2.3.6"
solana-hash = "2.3.0"
solana-entry = "2.3.6"
solana-rpc-client-nonce-utils = "2.3.6"
solana-perf = "2.3.6"
solana-metrics = "2.3.6"
spl-token = "8.0.0"
spl-token-2022 = { version = "8.0.0", features = ["no-entrypoint"] }
spl-associated-token-account = "7.0.0"
borsh = { version = "1.5.3", features = ["derive"] }
isahc = "1.7.2"
serde = { version = "1.0.215", features = ["derive"] }
serde_json = "1.0.134"
futures = "0.3.31"
futures-util = "0.3.31"
base64 = "0.22.1"
bs58 = "0.5.1"
rand = "0.9.0"
bincode = "1.3.3"
anyhow = "1.0.90"
yellowstone-grpc-client = {  version = "8.0.0" }
yellowstone-grpc-proto = {  version = "8.0.0" }
reqwest = { version = "0.12.12", features = ["json", "multipart"] }
tokio = { version = "1.42.0" , features = ["full", "rt-multi-thread"]}
tonic = { version = "0.12.3", features = ["tls", "tls-roots", "tls-webpki-roots"] }
rustls = { version = "0.23.23", features = ["ring"] }
rustls-native-certs = "0.8.1"
tokio-rustls = "0.26.1"
core_affinity = "0.8"
log = "0.4.22"
chrono = "0.4.39"
regex = "1"
tracing = "0.1.41"
thiserror = "2.0.11"
async-trait = "0.1.86"
lazy_static = "1.5.0"
once_cell = "1.20.3"
prost = "0.13.5"
prost-types = "0.13.5"
num_enum = "0.7.3"
num-derive = "0.4.2"
num-traits = "0.2.19"
hex = "0.4.3"
bytemuck = { version = "1.4.0" }
arrayref = "0.3.6"
borsh-derive = "1.5.5"
indicatif = "0.18.0"
solana-system-interface = "1.0.0"
