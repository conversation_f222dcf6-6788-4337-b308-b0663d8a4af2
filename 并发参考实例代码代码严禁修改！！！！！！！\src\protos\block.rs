// This file is @generated by prost-build.
/// Condensed block helpful for getting data around efficiently internal to our system.
#[derive(<PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct CondensedBlock {
    #[prost(message, optional, tag = "1")]
    pub header: ::core::option::Option<super::shared::Header>,
    #[prost(string, tag = "2")]
    pub previous_blockhash: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(uint64, tag = "4")]
    pub parent_slot: u64,
    #[prost(bytes = "vec", repeated, tag = "5")]
    pub versioned_transactions: ::prost::alloc::vec::Vec<::prost::alloc::vec::Vec<u8>>,
    #[prost(uint64, tag = "6")]
    pub slot: u64,
    #[prost(string, tag = "7")]
    pub commitment: ::prost::alloc::string::String,
}
