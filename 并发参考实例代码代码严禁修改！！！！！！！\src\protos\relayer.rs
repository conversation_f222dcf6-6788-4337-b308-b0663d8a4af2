// This file is @generated by prost-build.
#[derive(<PERSON>lone, Co<PERSON>, PartialEq, ::prost::Message)]
pub struct GetTpuConfigsRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetTpuConfigsResponse {
    #[prost(message, optional, tag = "1")]
    pub tpu: ::core::option::Option<super::shared::Socket>,
    #[prost(message, optional, tag = "2")]
    pub tpu_forward: ::core::option::Option<super::shared::Socket>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribePacketsRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribePacketsResponse {
    #[prost(message, optional, tag = "1")]
    pub header: ::core::option::Option<super::shared::Header>,
    #[prost(oneof = "subscribe_packets_response::Msg", tags = "2, 3")]
    pub msg: ::core::option::Option<subscribe_packets_response::Msg>,
}
/// Nested message and enum types in `SubscribePacketsResponse`.
pub mod subscribe_packets_response {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Msg {
        #[prost(message, tag = "2")]
        Heartbeat(super::super::shared::Heartbeat),
        #[prost(message, tag = "3")]
        Batch(super::super::packet::PacketBatch),
    }
}
/// Generated client implementations.
pub mod relayer_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    /// / Relayers offer a TPU and TPU forward proxy for Solana validators.
    /// / Validators can connect and fetch the TPU configuration for the relayer and start to advertise the
    /// / relayer's information in gossip.
    /// / They can also subscribe to packets which arrived on the TPU ports at the relayer
    #[derive(Debug, Clone)]
    pub struct RelayerClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl RelayerClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> RelayerClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> RelayerClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            RelayerClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        /// The relayer has TPU and TPU forward sockets that validators can leverage.
        /// A validator can fetch this config and change its TPU and TPU forward port in gossip.
        pub async fn get_tpu_configs(
            &mut self,
            request: impl tonic::IntoRequest<super::GetTpuConfigsRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetTpuConfigsResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/relayer.Relayer/GetTpuConfigs",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("relayer.Relayer", "GetTpuConfigs"));
            self.inner.unary(req, path, codec).await
        }
        /// Validators can subscribe to packets from the relayer and receive a multiplexed signal that contains a mixture
        /// of packets and heartbeats
        pub async fn subscribe_packets(
            &mut self,
            request: impl tonic::IntoRequest<super::SubscribePacketsRequest>,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::SubscribePacketsResponse>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/relayer.Relayer/SubscribePackets",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("relayer.Relayer", "SubscribePackets"));
            self.inner.server_streaming(req, path, codec).await
        }
    }
}
