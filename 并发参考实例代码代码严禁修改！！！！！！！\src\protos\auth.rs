// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GenerateAuthChallengeRequest {
    /// / Role the client is attempting to generate tokens for.
    #[prost(enumeration = "Role", tag = "1")]
    pub role: i32,
    /// / Client's 32 byte pubkey.
    #[prost(bytes = "vec", tag = "2")]
    pub pubkey: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GenerateAuthChallengeResponse {
    #[prost(string, tag = "1")]
    pub challenge: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GenerateAuthTokensRequest {
    /// / The pre-signed challenge.
    #[prost(string, tag = "1")]
    pub challenge: ::prost::alloc::string::String,
    /// / The signing keypair's corresponding 32 byte pubkey.
    #[prost(bytes = "vec", tag = "2")]
    pub client_pubkey: ::prost::alloc::vec::Vec<u8>,
    /// / The 64 byte signature of the challenge signed by the client's private key. The private key must correspond to
    /// the pubkey passed in the \[GenerateAuthChallenge\] method. The client is expected to sign the challenge token
    /// prepended with their pubkey. For example sign(pubkey, challenge).
    #[prost(bytes = "vec", tag = "3")]
    pub signed_challenge: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Token {
    /// / The token.
    #[prost(string, tag = "1")]
    pub value: ::prost::alloc::string::String,
    /// / When the token will expire.
    #[prost(message, optional, tag = "2")]
    pub expires_at_utc: ::core::option::Option<::prost_types::Timestamp>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GenerateAuthTokensResponse {
    /// / The token granting access to resources.
    #[prost(message, optional, tag = "1")]
    pub access_token: ::core::option::Option<Token>,
    /// / The token used to refresh the access_token. This has a longer TTL than the access_token.
    #[prost(message, optional, tag = "2")]
    pub refresh_token: ::core::option::Option<Token>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RefreshAccessTokenRequest {
    /// / Non-expired refresh token obtained from the \[GenerateAuthTokens\] method.
    #[prost(string, tag = "1")]
    pub refresh_token: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RefreshAccessTokenResponse {
    /// / Fresh access_token.
    #[prost(message, optional, tag = "1")]
    pub access_token: ::core::option::Option<Token>,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum Role {
    Relayer = 0,
    Searcher = 1,
    Validator = 2,
    ShredstreamSubscriber = 3,
}
impl Role {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Relayer => "RELAYER",
            Self::Searcher => "SEARCHER",
            Self::Validator => "VALIDATOR",
            Self::ShredstreamSubscriber => "SHREDSTREAM_SUBSCRIBER",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "RELAYER" => Some(Self::Relayer),
            "SEARCHER" => Some(Self::Searcher),
            "VALIDATOR" => Some(Self::Validator),
            "SHREDSTREAM_SUBSCRIBER" => Some(Self::ShredstreamSubscriber),
            _ => None,
        }
    }
}
/// Generated client implementations.
pub mod auth_service_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    /// / This service is responsible for issuing auth tokens to clients for API access.
    #[derive(Debug, Clone)]
    pub struct AuthServiceClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl AuthServiceClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> AuthServiceClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> AuthServiceClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            AuthServiceClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        /// / Returns a challenge, client is expected to sign this challenge with an appropriate keypair in order to obtain access tokens.
        pub async fn generate_auth_challenge(
            &mut self,
            request: impl tonic::IntoRequest<super::GenerateAuthChallengeRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GenerateAuthChallengeResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/auth.AuthService/GenerateAuthChallenge",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("auth.AuthService", "GenerateAuthChallenge"));
            self.inner.unary(req, path, codec).await
        }
        /// / Provides the client with the initial pair of auth tokens for API access.
        pub async fn generate_auth_tokens(
            &mut self,
            request: impl tonic::IntoRequest<super::GenerateAuthTokensRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GenerateAuthTokensResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/auth.AuthService/GenerateAuthTokens",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("auth.AuthService", "GenerateAuthTokens"));
            self.inner.unary(req, path, codec).await
        }
        /// / Call this method with a non-expired refresh token to obtain a new access token.
        pub async fn refresh_access_token(
            &mut self,
            request: impl tonic::IntoRequest<super::RefreshAccessTokenRequest>,
        ) -> std::result::Result<
            tonic::Response<super::RefreshAccessTokenResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/auth.AuthService/RefreshAccessToken",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("auth.AuthService", "RefreshAccessToken"));
            self.inner.unary(req, path, codec).await
        }
    }
}
