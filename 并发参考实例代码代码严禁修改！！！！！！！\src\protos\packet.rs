// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PacketBatch {
    #[prost(message, repeated, tag = "1")]
    pub packets: ::prost::alloc::vec::Vec<Packet>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Packet {
    #[prost(bytes = "vec", tag = "1")]
    pub data: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, optional, tag = "2")]
    pub meta: ::core::option::Option<Meta>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Meta {
    #[prost(uint64, tag = "1")]
    pub size: u64,
    #[prost(string, tag = "2")]
    pub addr: ::prost::alloc::string::String,
    #[prost(uint32, tag = "3")]
    pub port: u32,
    #[prost(message, optional, tag = "4")]
    pub flags: ::core::option::Option<PacketFlags>,
    #[prost(uint64, tag = "5")]
    pub sender_stake: u64,
}
#[derive(C<PERSON>, Copy, PartialEq, ::prost::Message)]
pub struct PacketFlags {
    #[prost(bool, tag = "1")]
    pub discard: bool,
    #[prost(bool, tag = "2")]
    pub forwarded: bool,
    #[prost(bool, tag = "3")]
    pub repair: bool,
    #[prost(bool, tag = "4")]
    pub simple_vote_tx: bool,
    #[prost(bool, tag = "5")]
    pub tracer_packet: bool,
    #[prost(bool, tag = "6")]
    pub from_staked_node: bool,
}
