use solana_sdk::{pubkey::Pubkey, hash::Hash};
use std::sync::{Mutex, OnceLock};

/// Nonce信息结构体（向后兼容）
#[derive(Clone)]
pub struct NonceInfo {
    /// nonce账户地址
    pub nonce_account: Option<Pubkey>,
    /// 当前nonce值
    pub current_nonce: Hash,
    /// 是否已使用
    pub used: bool,
}

/// 全局Nonce缓存（向后兼容单账户模式）
pub struct NonceCache {
    /// 内部存储的Nonce信息
    nonce_info: Mutex<NonceInfo>,
}

// 使用静态OnceLock确保单例
static NONCE_CACHE: OnceLock<NonceCache> = OnceLock::new();

impl NonceCache {
    /// 获取单例实例
    pub fn get_instance() -> &'static NonceCache {
        NONCE_CACHE.get_or_init(|| NonceCache {
            nonce_info: Mutex::new(NonceInfo {
                nonce_account: None,
                current_nonce: Hash::default(),
                used: false,
            }),
        })
    }

    /// 初始化nonce信息
    pub fn init(&self, nonce_account: Option<Pubkey>) {
        let mut info = self.nonce_info.lock().unwrap();
        info.nonce_account = nonce_account;
        info.used = false;
    }

    /// 获取nonce信息副本
    pub fn get_nonce_info(&self) -> NonceInfo {
        self.nonce_info.lock().unwrap().clone()
    }

    /// 更新当前nonce值
    pub fn update_current_nonce(&self, current_nonce: Hash) {
        let mut info = self.nonce_info.lock().unwrap();
        let old_nonce = info.current_nonce;
        let was_used = info.used;
        info.current_nonce = current_nonce;
        info.used = false; // 新nonce可用
        tracing::debug!("🔄 单账户Nonce缓存已更新: {} -> {} (之前使用状态: {})", old_nonce, current_nonce, was_used);
    }

    /// 标记nonce已使用
    pub fn mark_used(&self) {
        let mut info = self.nonce_info.lock().unwrap();
        info.used = true;
    }
}
