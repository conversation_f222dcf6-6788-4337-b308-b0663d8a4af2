// This file is @generated by prost-build.
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct Header {
    #[prost(message, optional, tag = "1")]
    pub ts: ::core::option::Option<::prost_types::Timestamp>,
}
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct Heartbeat {
    #[prost(uint64, tag = "1")]
    pub count: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Socket {
    #[prost(string, tag = "1")]
    pub ip: ::prost::alloc::string::String,
    #[prost(int64, tag = "2")]
    pub port: i64,
}
